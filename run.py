import asyncio
import json
import logging
import os
import random
from decimal import Decimal, ROUND_DOWN
from typing import Dict, List, Set, Tuple
from web3 import Web3
from eth_account import Account
import time
import aiofiles

class WalletInfo:
    def __init__(self, original: str):
        self.original = original.strip()  # 原始私钥
        self.address = None  # 以太坊地址
        self.nonce = None  # 当前nonce值
        self.initial_nonce = None  # 初始nonce值
        self.current_gas_gwei = None  # 当前使用的gas费率
        self._process_private_key()
    
    def _process_private_key(self):
        """处理私钥输入"""
        private_key = self.original.lower().replace('0x', '')
        
        if len(private_key) != 64:
            raise ValueError(f"无效的私钥长度: {len(private_key)}，应为64个字符")
            
        try:
            int(private_key, 16)
        except ValueError:
            raise ValueError("私钥必须是有效的十六进制字符串")
            
        try:
            if not private_key.startswith('0x'):
                private_key = '0x' + private_key
            account = Account.from_key(private_key)
            self.address = account.address
        except Exception as e:
            raise ValueError(f"无效的私钥格式: {self.original}")

class Web3Pool:
    def __init__(self, rpc_urls: List[str], pool_size: int = 10):
        self.rpc_urls = rpc_urls
        random.shuffle(self.rpc_urls)  # 随机打乱节点顺序
        self.connections = []
        self.semaphore = asyncio.Semaphore(pool_size)
        self.current_index = 0
        self.lock = asyncio.Lock()
        self.query_connection = None  # 查询专用连接
        self.task_queue = None  # 添加对TaskQueue的引用
        
    def set_task_queue(self, task_queue):
        """设置TaskQueue引用"""
        self.task_queue = task_queue
        
    async def initialize(self):
        """初始化连接池"""
        for rpc_url in self.rpc_urls:
            w3 = Web3(Web3.HTTPProvider(rpc_url))
            self.connections.append(w3)
            
    def initialize_query_connection(self, query_rpc: str):
        """初始化查询专用连接"""
        self.query_connection = Web3(Web3.HTTPProvider(query_rpc))
            
    async def get_connection(self) -> Web3:
        """获取一个可用的Web3连接"""
        async with self.semaphore:
            async with self.lock:
                self.current_index = (self.current_index + 1) % len(self.connections)
                return self.connections[self.current_index]
                
    def get_random_connection(self) -> Web3:
        """获取随机连接"""
        return random.choice(self.connections)
        
    def get_query_connection(self) -> Web3:
        """获取查询专用连接"""
        return self.query_connection

class ProjectBase:
    def __init__(self, wallet: WalletInfo, config: dict):
        self.wallet = wallet
        self.config = config
        self.w3 = None  # Web3实例将由TaskQueue在执行时设置
        self.query_w3 = None  # 查询专用Web3实例
        
    async def wait_for_transaction(self, tx_hash: str, action_name: str, wait_confirm: bool = False) -> bool:
        """等待交易确认"""
        try:
            if not wait_confirm:
                # 如果不需要等待确认，直接返回成功
                logging.info(f"交易已提交但不等待确认: {action_name}, 交易哈希: 0x{tx_hash}")
                return True
                
            # 以下是需要等待确认的逻辑
            logging.info(f"等待交易确认: {action_name}, 交易哈希: 0x{tx_hash}")
            # 设置等待超时时间为5分钟
            start_time = time.time()
            timeout = 300
            
            receipt = await asyncio.to_thread(
                self.w3.eth.wait_for_transaction_receipt,
                tx_hash,
                timeout=timeout  # 5分钟超时
            )
            if receipt['status'] == 1:
                logging.info(f"{self.get_wallet_prefix()} {action_name}交易已确认! Gas使用: {receipt['gasUsed']}")
                return True
            else:
                logging.error(f"{self.get_wallet_prefix()} {action_name}交易失败!")
                return False
        except Exception as e:
            logging.error(f"{self.get_wallet_prefix()} 等待{action_name}交易确认失败: {str(e)}")
            return False

    async def send_transaction_with_retry(self, signed_tx, action_name: str, wait_confirm: bool = False) -> bool:
        """发送交易并重试"""
        try:
            # 获取交易哈希 - 修复：使用raw_transaction属性
            tx_hash = self.w3.eth.send_raw_transaction(signed_tx.raw_transaction)
            # 只有在交易成功发送后才递增nonce
            self.wallet.nonce += 1
            logging.info(f"{self.get_wallet_prefix()} {action_name}交易已发送! 交易哈希: {tx_hash.hex()} (nonce: {self.wallet.nonce-1})")
            
            # 等待交易确认
            success = await self.wait_for_transaction(tx_hash.hex(), action_name, wait_confirm)
            return success
            
        except Exception as e:
            error_msg = str(e).lower()
            # 如果是nonce错误或者replacement transaction underpriced，直接返回失败
            if 'replacement transaction underpriced' in error_msg:
                logging.error(f"{self.get_wallet_prefix()} {action_name}发送失败: {str(e)}")
                return False
                
            # 如果是nonce错误，重新获取nonce
            if 'nonce too low' in error_msg or 'nonce too high' in error_msg:
                try:
                    current_nonce = self.w3.eth.get_transaction_count(self.wallet.address)
                    logging.warning(f"{self.get_wallet_prefix()} Nonce错误 (当前: {self.wallet.nonce}, 链上: {current_nonce})，重置为链上nonce")
                    self.wallet.nonce = current_nonce
                    return False
                except Exception as ne:
                    logging.error(f"{self.get_wallet_prefix()} 获取nonce失败: {str(ne)}")
                    return False
            
            logging.error(f"{self.get_wallet_prefix()} {action_name}发送失败: {str(e)}")
            return False

    async def get_gas_price(self) -> int:
        """获取gas price"""
        try:
            # 使用查询专用RPC获取实时费率
            if self.query_w3 is None:
                self.query_w3 = await self.get_web3_for_query()
                
            if not self.query_w3:
                logging.error(f"{self.get_wallet_prefix()} 查询专用RPC连接未初始化")
                return int(1000 * 10**9)  # 使用默认gas价格
                
            base_gas_price = self.query_w3.eth.gas_price
            gas_price_gwei = base_gas_price / 10**9
            
            # 在当前gas基础上增加10%
            gas_price_with_buffer = base_gas_price * 1.1
            
            # 如果设置了最低gas阈值且当前gas低于该值，则在最低值基础上随机增加0.01-0.9
            if hasattr(self.w3, 'web3_pool') and hasattr(self.w3.web3_pool, 'task_queue'):
                min_gas_threshold = self.w3.web3_pool.task_queue.min_gas_threshold
                if min_gas_threshold > 0 and gas_price_gwei < min_gas_threshold:
                    random_addition = random.uniform(0.01, 0.9)
                    adjusted_gas = min_gas_threshold + random_addition
                    logging.info(f"{self.get_wallet_prefix()} 当前gas ({gas_price_gwei:.2f} Gwei) 低于最低阈值，调整为: {adjusted_gas:.2f} Gwei")
                    return int(adjusted_gas * 10**9)
            
            self.wallet.current_gas_gwei = gas_price_gwei
            logging.info(f"{self.get_wallet_prefix()} 使用当前费率: {gas_price_gwei:.2f} Gwei (+10% = {(gas_price_gwei * 1.1):.2f} Gwei)")
            return int(gas_price_with_buffer)
            
        except Exception as e:
            logging.error(f"{self.get_wallet_prefix()} 获取gas价格失败: {str(e)}")
            # 使用默认gas价格
            default_gwei = 1000
            return int(default_gwei * 10**9)
            
    def get_wallet_prefix(self) -> str:
        """获取钱包日志前缀"""
        # 缩短钱包地址显示，只显示前6位和后4位
        short_address = f"{self.wallet.address[:6]}...{self.wallet.address[-4:]}"
        return f"钱包 {short_address}"
        
    def should_use_query_rpc(self) -> bool:
        """检查是否应该使用查询专用RPC"""
        project_settings = self.config['project_settings'].get(self.__class__.__name__.lower().replace('project', ''), {})
        return project_settings.get('use_query_rpc', False)
        
    async def get_web3_for_query(self) -> Web3:
        """获取用于查询的Web3实例"""
        if self.should_use_query_rpc():
            if self.query_w3 is None:
                self.query_w3 = self.w3.web3_pool.get_query_connection()
            return self.query_w3
        return self.w3
                
    async def execute(self) -> bool:
        """执行Lido+EigenLayer质押流程"""
        try:
            # 1. 质押ETH到Lido
            success = await self.stake_to_lido()
            if not success:
                return False
                
            # 等待5-10秒
            delay = random.uniform(5, 10)
            logging.info(f"等待 {delay:.1f} 秒...")
            await asyncio.sleep(delay)
            
            # 2. 授权stETH
            success = await self.approve_steth()
            if not success:
                return False
                
            # 等待5-10秒
            delay = random.uniform(5, 10)
            logging.info(f"等待 {delay:.1f} 秒...")
            await asyncio.sleep(delay)
            
            # 3. 质押stETH到EigenLayer
            success = await self.stake_to_eigenlayer()
            return success
            
        except Exception as e:
            logging.error(f"Lido+EigenLayer质押流程失败: {str(e)}")
            return False

class LidoEigenLayerStakProject(ProjectBase):
    def __init__(self, wallet: WalletInfo, config: dict):
        super().__init__(wallet, config)
        self.project_config = config['project_settings']['lido_eigenlayer_stak']
        # 将合约地址转换为checksum格式
        self.lido_contract = Web3.to_checksum_address(self.project_config['lido_contract'])
        self.eigenlayer_contract = Web3.to_checksum_address(self.project_config['eigenlayer_contract'])
        self.referral = Web3.to_checksum_address(self.project_config['referral'])
        self.strategy = Web3.to_checksum_address(self.project_config['strategy'])
        
        # Lido stETH ABI
        self.lido_abi = [{
            "inputs": [{"name": "_referral", "type": "address"}],
            "name": "submit",
            "outputs": [],
            "stateMutability": "payable",
            "type": "function"
        }, {
            "constant": True,
            "inputs": [{"name": "account", "type": "address"}],
            "name": "balanceOf",
            "outputs": [{"name": "", "type": "uint256"}],
            "payable": False,
            "stateMutability": "view",
            "type": "function"
        }, {
            "constant": False,
            "inputs": [
                {"name": "_spender", "type": "address"},
                {"name": "_amount", "type": "uint256"}
            ],
            "name": "approve",
            "outputs": [{"name": "", "type": "bool"}],
            "payable": False,
            "stateMutability": "nonpayable",
            "type": "function"
        }]
        
        # EigenLayer ABI
        self.eigenlayer_abi = [{
            "inputs": [
                {"name": "strategy", "type": "address"},
                {"name": "token", "type": "address"},
                {"name": "amount", "type": "uint256"}
            ],
            "name": "depositIntoStrategy",
            "outputs": [],
            "stateMutability": "nonpayable",
            "type": "function"
        }]
        
    async def stake_to_lido(self) -> bool:
        """质押ETH到Lido"""
        try:
            contract = self.w3.eth.contract(
                address=self.lido_contract,
                abi=self.lido_abi
            )
            
            # 随机质押数量
            min_amount = Decimal(self.project_config['min_amount'])
            max_amount = Decimal(self.project_config['max_amount'])
            amount = Decimal(str(random.uniform(float(min_amount), float(max_amount)))).quantize(Decimal('0.000001'), rounding=ROUND_DOWN)
            amount_wei = int(amount * 10**18)
            
            gas_price = await self.get_gas_price()
            gas_limit = random.randint(120999, 135000)
            
            tx = contract.functions.submit(
                self.referral
            ).build_transaction({
                'from': self.wallet.address,
                'value': amount_wei,
                'nonce': self.wallet.nonce,
                'gas': gas_limit,
                'maxFeePerGas': gas_price,
                'maxPriorityFeePerGas': gas_price,
                'chainId': 1  # 以太坊主网
            })
            
            signed_tx = self.w3.eth.account.sign_transaction(tx, self.wallet.original)
            success = await self.send_transaction_with_retry(signed_tx, f"质押ETH到Lido (数量: {amount} ETH)", True)
            return success
            
        except Exception as e:
            logging.error(f"质押ETH到Lido失败: {str(e)}")
            return False
            
    async def approve_steth(self) -> bool:
        """授权stETH"""
        try:
            contract = self.w3.eth.contract(
                address=self.lido_contract,
                abi=self.lido_abi
            )
            
            gas_price = await self.get_gas_price()
            gas_limit = random.randint(124757, 130000)
            
            max_amount = 2**256 - 1
            
            tx = contract.functions.approve(
                self.eigenlayer_contract,
                max_amount
            ).build_transaction({
                'from': self.wallet.address,
                'nonce': self.wallet.nonce,
                'gas': gas_limit,
                'maxFeePerGas': gas_price,
                'maxPriorityFeePerGas': gas_price,
                'chainId': 1  # 以太坊主网
            })
            
            signed_tx = self.w3.eth.account.sign_transaction(tx, self.wallet.original)
            success = await self.send_transaction_with_retry(signed_tx, "授权stETH", True)
            return success
            
        except Exception as e:
            logging.error(f"授权stETH失败: {str(e)}")
            return False
            
    async def stake_to_eigenlayer(self) -> bool:
        """质押stETH到EigenLayer"""
        try:
            # 获取stETH余额
            lido_contract = self.w3.eth.contract(
                address=self.lido_contract,
                abi=self.lido_abi
            )
            
            balance = lido_contract.functions.balanceOf(self.wallet.address).call()
            if balance == 0:
                logging.info(f"{self.get_wallet_prefix()} stETH余额为0，跳过质押到EigenLayer")
                return False
                
            eigenlayer_contract = self.w3.eth.contract(
                address=self.eigenlayer_contract,
                abi=self.eigenlayer_abi
            )
            
            gas_price = await self.get_gas_price()
            gas_limit = random.randint(351867, 370000)
            
            tx = eigenlayer_contract.functions.depositIntoStrategy(
                self.strategy,
                self.lido_contract,
                balance  # 质押全部余额
            ).build_transaction({
                'from': self.wallet.address,
                'nonce': self.wallet.nonce,
                'gas': gas_limit,
                'maxFeePerGas': gas_price,
                'maxPriorityFeePerGas': gas_price,
                'chainId': 1  # 以太坊主网
            })
            
            signed_tx = self.w3.eth.account.sign_transaction(tx, self.wallet.original)
            success = await self.send_transaction_with_retry(signed_tx, f"质押stETH到EigenLayer (数量: {balance / 10**18:.6f} stETH)")
            return success
            
        except Exception as e:
            logging.error(f"质押stETH到EigenLayer失败: {str(e)}")
            return False
            
    async def execute(self) -> bool:
        """执行Lido+EigenLayer质押流程"""
        try:
            # 1. 质押ETH到Lido
            success = await self.stake_to_lido()
            if not success:
                return False
                
            # 等待5-10秒
            delay = random.uniform(5, 10)
            logging.info(f"等待 {delay:.1f} 秒...")
            await asyncio.sleep(delay)
            
            # 2. 授权stETH
            success = await self.approve_steth()
            if not success:
                return False
                
            # 等待5-10秒
            delay = random.uniform(5, 10)
            logging.info(f"等待 {delay:.1f} 秒...")
            await asyncio.sleep(delay)
            
            # 3. 质押stETH到EigenLayer
            success = await self.stake_to_eigenlayer()
            return success
            
        except Exception as e:
            logging.error(f"Lido+EigenLayer质押流程失败: {str(e)}")
            return False

class AaveSupplyProject(ProjectBase):
    def __init__(self, wallet: WalletInfo, config: dict):
        super().__init__(wallet, config)
        self.project_config = config['project_settings']['aave_supply']
        # 将合约地址转换为checksum格式
        self.aave_contract = Web3.to_checksum_address(self.project_config['contract_address'])  # 修改这里
        self.pool_address = Web3.to_checksum_address(self.project_config['pool_address'])
        
        # Aave Supply ABI
        self.aave_abi = [{
            "inputs": [
                {"name": "pool", "type": "address"},
                {"name": "onBehalfOf", "type": "address"},
                {"name": "referralCode", "type": "uint16"}
            ],
            "name": "depositETH",
            "outputs": [],
            "stateMutability": "payable",
            "type": "function"
        }]
        
    async def supply_eth(self) -> bool:
        """向Aave存入ETH"""
        try:
            contract = self.w3.eth.contract(
                address=self.aave_contract,
                abi=self.aave_abi
            )
            
            # 随机存入数量
            min_amount = Decimal(self.project_config['min_amount'])
            max_amount = Decimal(self.project_config['max_amount'])
            amount = Decimal(str(random.uniform(float(min_amount), float(max_amount)))).quantize(Decimal('0.000001'), rounding=ROUND_DOWN)
            amount_wei = int(amount * 10**18)
            
            gas_price = await self.get_gas_price()
            gas_limit = 300000  # 固定gas limit
            
            tx = contract.functions.depositETH(
                self.pool_address,
                self.wallet.address,  # onBehalfOf 使用当前钱包地址
                0  # referralCode
            ).build_transaction({
                'from': self.wallet.address,
                'value': amount_wei,
                'nonce': self.wallet.nonce,
                'gas': gas_limit,
                'maxFeePerGas': gas_price,
                'maxPriorityFeePerGas': gas_price,
                'chainId': 1  # 以太坊主网
            })
            
            signed_tx = self.w3.eth.account.sign_transaction(tx, self.wallet.original)
            success = await self.send_transaction_with_retry(signed_tx, f"向Aave存入ETH (数量: {amount} ETH)")
            return success
            
        except Exception as e:
            logging.error(f"向Aave存入ETH失败: {str(e)}")
            return False
            
    async def execute(self) -> bool:
        """执行Aave存入ETH流程"""
        try:
            # 执行存入操作
            success = await self.supply_eth()
            return success
            
        except Exception as e:
            logging.error(f"Aave存入ETH流程失败: {str(e)}")
            return False

class WethDepositProject(ProjectBase):
    def __init__(self, wallet: WalletInfo, config: dict):
        super().__init__(wallet, config)
        self.project_config = config['project_settings']['weth_deposit']
        # 将合约地址转换为checksum格式
        self.weth_contract = Web3.to_checksum_address(self.project_config['weth_contract'])
        
        # WETH ABI
        self.weth_abi = [{
            "constant": False,
            "inputs": [],
            "name": "deposit",
            "outputs": [],
            "payable": True,
            "stateMutability": "payable",
            "type": "function"
        }]
        
    async def deposit_eth(self) -> bool:
        """存入ETH兑换WETH"""
        try:
            contract = self.w3.eth.contract(
                address=self.weth_contract,
                abi=self.weth_abi
            )
            
            # 随机存入数量
            min_amount = Decimal(self.project_config['min_amount'])
            max_amount = Decimal(self.project_config['max_amount'])
            amount = Decimal(str(random.uniform(float(min_amount), float(max_amount)))).quantize(Decimal('0.000001'), rounding=ROUND_DOWN)
            amount_wei = int(amount * 10**18)
            
            gas_price = await self.get_gas_price()
            gas_limit = random.randint(60000, 80000)  # 随机gas limit
            
            tx = contract.functions.deposit().build_transaction({
                'from': self.wallet.address,
                'value': amount_wei,
                'nonce': self.wallet.nonce,
                'gas': gas_limit,
                'maxFeePerGas': gas_price,
                'maxPriorityFeePerGas': gas_price,
                'chainId': 1  # 以太坊主网
            })
            
            signed_tx = self.w3.eth.account.sign_transaction(tx, self.wallet.original)
            success = await self.send_transaction_with_retry(signed_tx, f"存入ETH兑换WETH (数量: {amount} ETH)")
            return success
            
        except Exception as e:
            logging.error(f"存入ETH兑换WETH失败: {str(e)}")
            return False
            
    async def execute(self) -> bool:
        """执行WETH兑换流程"""
        try:
            # 执行兑换操作
            success = await self.deposit_eth()
            return success
            
        except Exception as e:
            logging.error(f"WETH兑换流程失败: {str(e)}")
            return False

class ArbitrumBridgeProject(ProjectBase):
    def __init__(self, wallet: WalletInfo, config: dict):
        super().__init__(wallet, config)
        self.project_config = config['project_settings']['arbitrum_bridge']
        # 将合约地址转换为checksum格式
        self.bridge_contract = Web3.to_checksum_address(self.project_config['bridge_contract'])
        
        # Arbitrum Bridge ABI
        self.bridge_abi = [{
            "inputs": [],
            "name": "depositEth",
            "outputs": [],
            "stateMutability": "payable",
            "type": "function"
        }]
        
    async def deposit_eth(self) -> bool:
        """通过Bridge存入ETH到Arbitrum"""
        try:
            contract = self.w3.eth.contract(
                address=self.bridge_contract,
                abi=self.bridge_abi
            )
            
            # 随机存入数量
            min_amount = Decimal(self.project_config['min_amount'])
            max_amount = Decimal(self.project_config['max_amount'])
            amount = Decimal(str(random.uniform(float(min_amount), float(max_amount)))).quantize(Decimal('0.000001'), rounding=ROUND_DOWN)
            amount_wei = int(amount * 10**18)
            
            gas_price = await self.get_gas_price()
            gas_limit = random.randint(95000, 100000)  # 随机gas limit
            
            tx = contract.functions.depositEth().build_transaction({
                'from': self.wallet.address,
                'value': amount_wei,
                'nonce': self.wallet.nonce,
                'gas': gas_limit,
                'maxFeePerGas': gas_price,
                'maxPriorityFeePerGas': gas_price,
                'chainId': 1  # 以太坊主网
            })
            
            signed_tx = self.w3.eth.account.sign_transaction(tx, self.wallet.original)
            success = await self.send_transaction_with_retry(signed_tx, f"通过Bridge存入ETH到Arbitrum (数量: {amount} ETH)")
            return success
            
        except Exception as e:
            logging.error(f"通过Bridge存入ETH到Arbitrum失败: {str(e)}")
            return False
            
    async def execute(self) -> bool:
        """执行Arbitrum Bridge存入ETH流程"""
        try:
            # 执行存入操作
            success = await self.deposit_eth()
            return success
            
        except Exception as e:
            logging.error(f"Arbitrum Bridge存入ETH流程失败: {str(e)}")
            return False

class MintNftEternalechoesProject(ProjectBase):
    def __init__(self, wallet: WalletInfo, config: dict):
        super().__init__(wallet, config)
        self.project_config = config['project_settings']['mint_nft_eternalechoes']
        # 将合约地址转换为checksum格式
        self.nft_contract = Web3.to_checksum_address(self.project_config['nft_contract'])
        
        # Mint NFT ABI
        self.nft_abi = [{
            "inputs": [
                {"name": "to", "type": "address"},
                {"name": "qty", "type": "uint256"}
            ],
            "name": "mintPublic",
            "outputs": [],
            "stateMutability": "nonpayable",
            "type": "function"
        }]
        
    async def mint_nft(self) -> bool:
        """铸造NFT"""
        try:
            contract = self.w3.eth.contract(
                address=self.nft_contract,
                abi=self.nft_abi
            )
            
            gas_price = await self.get_gas_price()
            
            # 随机生成5-10之间的铸造数量
            mint_quantity = random.randint(5, 10)
            
            # 根据铸造数量调整gas limit，多个NFT需要更多gas
            base_gas = 150000
            gas_per_nft = 30000
            gas_limit = random.randint(base_gas + mint_quantity * gas_per_nft, 
                                       base_gas + mint_quantity * gas_per_nft + 50000)
            
            tx = contract.functions.mintPublic(
                self.wallet.address,  # 铸造到当前钱包
                mint_quantity  # 铸造数量为随机的5-10个
            ).build_transaction({
                'from': self.wallet.address,
                'nonce': self.wallet.nonce,
                'gas': gas_limit,
                'maxFeePerGas': gas_price,
                'maxPriorityFeePerGas': gas_price,
                'chainId': 1  # 以太坊主网
            })
            
            signed_tx = self.w3.eth.account.sign_transaction(tx, self.wallet.original)
            success = await self.send_transaction_with_retry(signed_tx, f"铸造{mint_quantity}个NFT到当前钱包地址")
            return success
            
        except Exception as e:
            logging.error(f"铸造NFT失败: {str(e)}")
            return False
            
    async def execute(self) -> bool:
        """执行NFT铸造流程"""
        try:
            # 执行铸造操作
            success = await self.mint_nft()
            return success
            
        except Exception as e:
            logging.error(f"NFT铸造流程失败: {str(e)}")
            return False

class MintNftCarellaProject(ProjectBase):
    def __init__(self, wallet: WalletInfo, config: dict):
        super().__init__(wallet, config)
        self.project_config = config['project_settings']['mint_nft_carella']
        # 将合约地址转换为checksum格式
        self.nft_contract = Web3.to_checksum_address(self.project_config['nft_contract'])
        
        # Mint NFT ABI
        self.nft_abi = [{
            "inputs": [
                {"name": "quantity", "type": "uint256"}
            ],
            "name": "purchase",
            "outputs": [],
            "stateMutability": "nonpayable",
            "type": "function"
        }]
        
    async def mint_nft(self) -> bool:
        """铸造CARELLA NFT"""
        try:
            contract = self.w3.eth.contract(
                address=self.nft_contract,
                abi=self.nft_abi
            )
            
            gas_price = await self.get_gas_price()
            gas_limit = random.randint(170000, 190000)  # 随机gas limit
            
            tx = contract.functions.purchase(
                1  # quantity 参数为1
            ).build_transaction({
                'from': self.wallet.address,
                'nonce': self.wallet.nonce,
                'gas': gas_limit,
                'maxFeePerGas': gas_price,
                'maxPriorityFeePerGas': gas_price,
                'chainId': 1  # 以太坊主网
            })
            
            signed_tx = self.w3.eth.account.sign_transaction(tx, self.wallet.original)
            success = await self.send_transaction_with_retry(signed_tx, "铸造CARELLA NFT到当前钱包地址")
            return success
            
        except Exception as e:
            logging.error(f"铸造CARELLA NFT失败: {str(e)}")
            return False
            
    async def execute(self) -> bool:
        """执行CARELLA NFT铸造流程"""
        try:
            # 执行铸造操作
            success = await self.mint_nft()
            return success
            
        except Exception as e:
            logging.error(f"CARELLA NFT铸造流程失败: {str(e)}")
            return False

class MintNftAAAAProject(ProjectBase):
    def __init__(self, wallet: WalletInfo, config: dict):
        super().__init__(wallet, config)
        self.project_config = config['project_settings']['mint_nft_aaaa']
        # 将合约地址转换为checksum格式
        self.nft_contract = Web3.to_checksum_address(self.project_config['nft_contract'])
        
        # Mint NFT ABI
        self.nft_abi = [{
            "inputs": [
                {"name": "to", "type": "address"},
                {"name": "qty", "type": "uint256"}
            ],
            "name": "mintPublic",
            "outputs": [],
            "stateMutability": "nonpayable",
            "type": "function"
        }]
        
    async def mint_nft(self) -> bool:
        """铸造AAAA NFT"""
        try:
            contract = self.w3.eth.contract(
                address=self.nft_contract,
                abi=self.nft_abi
            )
            
            gas_price = await self.get_gas_price()
            
            # 随机生成5-10之间的铸造数量
            mint_quantity = random.randint(5, 10)
            
            # 根据铸造数量调整gas limit
            gas_limit = random.randint(170000, 190000)
            
            tx = contract.functions.mintPublic(
                self.wallet.address,  # 铸造到当前钱包
                mint_quantity  # 铸造数量为随机的5-10个
            ).build_transaction({
                'from': self.wallet.address,
                'nonce': self.wallet.nonce,
                'gas': gas_limit,
                'maxFeePerGas': gas_price,
                'maxPriorityFeePerGas': gas_price,
                'chainId': 1  # 以太坊主网
            })
            
            signed_tx = self.w3.eth.account.sign_transaction(tx, self.wallet.original)
            success = await self.send_transaction_with_retry(signed_tx, f"铸造{mint_quantity}个AAAA NFT到当前钱包地址")
            return success
            
        except Exception as e:
            logging.error(f"铸造AAAA NFT失败: {str(e)}")
            return False
            
    async def execute(self) -> bool:
        """执行AAAA NFT铸造流程"""
        try:
            # 执行铸造操作
            success = await self.mint_nft()
            return success
            
        except Exception as e:
            logging.error(f"AAAA NFT铸造流程失败: {str(e)}")
            return False

class MintNftRuntomoonProject(ProjectBase):
    def __init__(self, wallet: WalletInfo, config: dict):
        super().__init__(wallet, config)
        self.project_config = config['project_settings']['mint_nft_runtomoon']
        # 将合约地址转换为checksum格式
        self.nft_contract = Web3.to_checksum_address(self.project_config['nft_contract'])
        
        # Mint NFT ABI
        self.nft_abi = [{
            "inputs": [
                {"name": "to", "type": "address"},
                {"name": "qty", "type": "uint256"}
            ],
            "name": "mintPublic",
            "outputs": [],
            "stateMutability": "nonpayable",
            "type": "function"
        }]
        
    async def mint_nft(self) -> bool:
        """铸造Runtomoon NFT"""
        try:
            contract = self.w3.eth.contract(
                address=self.nft_contract,
                abi=self.nft_abi
            )
            
            gas_price = await self.get_gas_price()
            
            # 随机生成5-10之间的铸造数量
            mint_quantity = random.randint(5, 10)
            
            # 设置gas limit
            gas_limit = random.randint(170000, 190000)
            
            tx = contract.functions.mintPublic(
                self.wallet.address,  # 铸造到当前钱包
                mint_quantity  # 铸造数量为随机的5-10个
            ).build_transaction({
                'from': self.wallet.address,
                'nonce': self.wallet.nonce,
                'gas': gas_limit,
                'maxFeePerGas': gas_price,
                'maxPriorityFeePerGas': gas_price,
                'chainId': 1  # 以太坊主网
            })
            
            signed_tx = self.w3.eth.account.sign_transaction(tx, self.wallet.original)
            success = await self.send_transaction_with_retry(signed_tx, f"铸造{mint_quantity}个Runtomoon NFT到当前钱包地址")
            return success
            
        except Exception as e:
            logging.error(f"铸造Runtomoon NFT失败: {str(e)}")
            return False
            
    async def execute(self) -> bool:
        """执行Runtomoon NFT铸造流程"""
        try:
            # 执行铸造操作
            success = await self.mint_nft()
            return success
            
        except Exception as e:
            logging.error(f"Runtomoon NFT铸造流程失败: {str(e)}")
            return False

class ApproveUsdtProject(ProjectBase):
    def __init__(self, wallet: WalletInfo, config: dict, project_name: str):
        super().__init__(wallet, config)
        self.project_config = config['project_settings'][project_name]
        # 获取USDT合约地址和spender地址
        self.usdt_contract = Web3.to_checksum_address(self.project_config['usdt_contract'])
        self.spender = Web3.to_checksum_address(self.project_config['spender'])
        self.project_name = project_name
        
        # USDT approve ABI
        self.usdt_abi = [{
            "constant": False,
            "inputs": [
                {"name": "_spender", "type": "address"},
                {"name": "_value", "type": "uint256"}
            ],
            "name": "approve",
            "outputs": [{"name": "", "type": "bool"}],
            "payable": False,
            "stateMutability": "nonpayable",
            "type": "function"
        }]
        
    def generate_random_value(self) -> int:
        """生成随机授权数量，确保尾数有6个0"""
        # 随机生成1000-10000之间的整数
        base = random.randint(1000, 10000)
        # 将其转换为有6个0的数字
        return base * 1000000
        
    async def approve(self) -> bool:
        """授权USDT"""
        try:
            contract = self.w3.eth.contract(
                address=self.usdt_contract,
                abi=self.usdt_abi
            )
            
            gas_price = await self.get_gas_price()
            gas_limit = random.randint(90000, 100000)  # 随机gas limit
            
            # 生成随机授权数量
            approve_value = self.generate_random_value()
            
            tx = contract.functions.approve(
                self.spender,
                approve_value
            ).build_transaction({
                'from': self.wallet.address,
                'nonce': self.wallet.nonce,
                'gas': gas_limit,
                'maxFeePerGas': gas_price,
                'maxPriorityFeePerGas': gas_price,
                'chainId': 1  # 以太坊主网
            })
            
            signed_tx = self.w3.eth.account.sign_transaction(tx, self.wallet.original)
            success = await self.send_transaction_with_retry(signed_tx, f"授权USDT给{self.project_name} (数量: {approve_value})")
            return success
            
        except Exception as e:
            logging.error(f"授权USDT失败: {str(e)}")
            return False
            
    async def execute(self) -> bool:
        """执行USDT授权流程"""
        try:
            # 执行授权操作
            success = await self.approve()
            return success
            
        except Exception as e:
            logging.error(f"USDT授权流程失败: {str(e)}")
            return False

class ApproveUsdtAggregationProject(ApproveUsdtProject):
    def __init__(self, wallet: WalletInfo, config: dict):
        super().__init__(wallet, config, 'approve_usdt_aggregation')

class ApproveUsdtUniswapProject(ApproveUsdtProject):
    def __init__(self, wallet: WalletInfo, config: dict):
        super().__init__(wallet, config, 'approve_usdt_uniswap')

class BlurDepositProject(ProjectBase):
    def __init__(self, wallet: WalletInfo, config: dict):
        super().__init__(wallet, config)
        self.project_config = config['project_settings']['blur_deposit']
        # 将合约地址转换为checksum格式
        self.blur_contract = Web3.to_checksum_address(self.project_config['blur_contract'])
        
        # Blur Deposit ABI
        self.blur_abi = [{
            "constant": False,
            "inputs": [],
            "name": "deposit",
            "outputs": [],
            "payable": True,
            "stateMutability": "payable",
            "type": "function"
        }]
        
    async def deposit_eth(self) -> bool:
        """向Blur存入ETH"""
        try:
            contract = self.w3.eth.contract(
                address=self.blur_contract,
                abi=self.blur_abi
            )
            
            # 随机存入数量
            min_amount = Decimal(self.project_config['min_amount'])
            max_amount = Decimal(self.project_config['max_amount'])
            amount = Decimal(str(random.uniform(float(min_amount), float(max_amount)))).quantize(Decimal('0.000001'), rounding=ROUND_DOWN)
            amount_wei = int(amount * 10**18)
            
            gas_price = await self.get_gas_price()
            gas_limit = random.randint(60000, 80000)  # 随机gas limit
            
            tx = contract.functions.deposit().build_transaction({
                'from': self.wallet.address,
                'value': amount_wei,
                'nonce': self.wallet.nonce,
                'gas': gas_limit,
                'maxFeePerGas': gas_price,
                'maxPriorityFeePerGas': gas_price,
                'chainId': 1  # 以太坊主网
            })
            
            signed_tx = self.w3.eth.account.sign_transaction(tx, self.wallet.original)
            success = await self.send_transaction_with_retry(signed_tx, f"向Blur存入ETH (数量: {amount} ETH)")
            return success
            
        except Exception as e:
            logging.error(f"向Blur存入ETH失败: {str(e)}")
            return False
            
    async def execute(self) -> bool:
        """执行Blur存入ETH流程"""
        try:
            # 执行存入操作
            success = await self.deposit_eth()
            return success
            
        except Exception as e:
            logging.error(f"Blur存入ETH流程失败: {str(e)}")
            return False

class TaskQueue:
    def __init__(self, max_size=1000):
        self.queue = asyncio.Queue(max_size)
        self.consumers = []
        self.web3_pool = None
        self.config = None
        self.project_classes = None
        self.running_tasks = set()
        self.lock = asyncio.Lock()
        self.user_name = None
        self.total_wallets = 0
        self.total_tasks = 0  # 新增：总任务数
        self.completed_tasks = 0  # 新增：已完成任务数
        self.progress_lock = asyncio.Lock()  # 新增：进度锁
        self.wallet_progress = {}  # 每个钱包的项目完成进度
        self.wallet_pending_projects = {}
        self.max_concurrent_tasks = 0
        self.task_semaphore = None
        self.gas_threshold = None  # gas阈值
        self.min_gas_threshold = 0  # 最低gas阈值
        self.is_paused = False  # 任务是否暂停
        self.pause_event = asyncio.Event()  # 用于暂停/恢复的事件
        self.pause_event.set()  # 初始状态为未暂停
        self.gas_monitor_task = None  # gas监控任务
        self.wallet_lock = asyncio.Lock()  # 钱包切换锁
        self.last_wallet_address = None  # 上一个处理的钱包地址
        self.wallet_sleep_min = 5  # 钱包切换最小休息时间
        self.wallet_sleep_max = 15  # 钱包切换最大休息时间
        
    async def producer(self, wallets: List[WalletInfo], wallet_pending_projects: Dict[str, List[str]]):
        """生产者：将钱包和项目组合放入队列"""
        self.total_wallets = len(wallets)
        self.wallet_pending_projects = wallet_pending_projects
        
        # 初始化查询专用连接
        if self.web3_pool and self.config:
            query_rpc = self.config.get('query_rpc')
            if query_rpc:
                self.web3_pool.initialize_query_connection(query_rpc)
        
        # 初始化钱包进度
        for wallet in wallets:
            pending_projects = wallet_pending_projects.get(wallet.address, [])
            if pending_projects:
                self.wallet_progress[wallet.address] = {
                    'total': len(pending_projects),
                    'completed': 0
                }
                
        # 创建钱包和项目的组合列表
        all_tasks = []
        for wallet in wallets:
            pending_projects = wallet_pending_projects.get(wallet.address, [])
            if pending_projects:
                projects = pending_projects.copy()
                random.shuffle(projects)
                for project in projects:
                    all_tasks.append((wallet, project))
        
        # 设置总任务数
        self.total_tasks = len(all_tasks)
        self.completed_tasks = 0
        
        # 随机打乱所有任务的顺序
        random.shuffle(all_tasks)
        
        # 将任务添加到队列
        for wallet, project in all_tasks:
            await self.queue.put((wallet, project))
            #logging.info(f"添加任务到队列: 钱包 {wallet.address[:6]}...{wallet.address[-4:]} - {project}")
                    
    async def get_wallet_progress(self, wallet: WalletInfo) -> str:
        """获取钱包进度字符串"""
        async with self.progress_lock:
            wallet_info = self.wallet_progress.get(wallet.address, {'total': 0, 'completed': 0})
            total_progress = f"[{self.completed_tasks}/{self.total_tasks}]"
            wallet_progress = f"({wallet_info['completed']}/{wallet_info['total']})"
            return f"{total_progress} {wallet_progress}"
                
    async def update_progress(self, wallet: WalletInfo, success: bool):
        """更新进度"""
        async with self.progress_lock:
            self.completed_tasks += 1
            if success and wallet.address in self.wallet_progress:
                self.wallet_progress[wallet.address]['completed'] += 1
                
    async def execute_task(self, wallet: WalletInfo, project: str):
        """执行单个任务"""
        try:
            # 获取钱包进度
            progress = await self.get_wallet_progress(wallet)
            
            # 检查项目是否仍需要执行
            if project not in self.wallet_pending_projects.get(wallet.address, []):
                logging.info(f"钱包 {progress} {wallet.address} 的项目 {project} 已完成，跳过执行")
                await self.update_progress(wallet, True)
                return
            
            # 获取Web3连接
            w3 = await self.web3_pool.get_connection()
            
            # 只在首次执行时初始化nonce
            if wallet.nonce is None:
                try:
                    wallet.nonce = w3.eth.get_transaction_count(wallet.address)
                    wallet.initial_nonce = wallet.nonce
                    logging.info(f"钱包 {progress} {wallet.address} 初始化nonce: {wallet.nonce}")
                except Exception as e:
                    logging.error(f"钱包 {progress} {wallet.address} 获取nonce失败: {str(e)}")
                    await self.save_result(project, wallet, False)
                    await self.update_progress(wallet, False)
                    return
            
            # 获取项目类
            project_class = self.project_classes.get(project)
            if not project_class:
                await self.save_result(project, wallet, False)
                await self.update_progress(wallet, False)
                return
                
            # 初始化项目
            project_instance = project_class(wallet, self.config)
            project_instance.w3 = w3
            
            # 添加进度信息到钱包前缀
            original_get_wallet_prefix = project_instance.get_wallet_prefix
            project_instance.get_wallet_prefix = lambda: f"钱包 {progress} {original_get_wallet_prefix()}"
            
            # 执行项目
            success = await project_instance.execute()
            
            # 保存结果
            await self.save_result(project, wallet, success)
            
            # 更新进度
            await self.update_progress(wallet, success)
            
            # 如果成功，从待完成项目列表中移除
            if success:
                if wallet.address in self.wallet_pending_projects:
                    try:
                        self.wallet_pending_projects[wallet.address].remove(project)
                    except ValueError:
                        pass
                
        except Exception as e:
            logging.error(f"执行任务失败 {wallet.address} - {project}: {str(e)}")
            await self.save_result(project, wallet, False)
            await self.update_progress(wallet, False)

    async def consumer(self, worker_id: int):
        """消费者：处理队列中的任务"""
        while True:
            try:
                # 等待未暂停状态
                await self.pause_event.wait()
                
                wallet, project = await self.queue.get()
                
                # 检查是否需要在钱包切换时休息
                async with self.wallet_lock:
                    if self.last_wallet_address is not None and self.last_wallet_address != wallet.address:
                        # 钱包切换，需要休息
                        sleep_time = random.uniform(self.wallet_sleep_min, self.wallet_sleep_max)
                        logging.info(f"Worker {worker_id} 钱包切换，休息 {sleep_time:.2f} 秒...")
                        await asyncio.sleep(sleep_time)
                    # 更新上一个处理的钱包地址
                    self.last_wallet_address = wallet.address
                
                # 使用信号量限制并发任务数
                async with self.task_semaphore:
                    task = asyncio.create_task(self.execute_task(wallet, project))
                    async with self.lock:
                        self.running_tasks.add(task)
                    task.add_done_callback(lambda t: self.task_done_callback(t))
                    
                    # 等待任务完成
                    await task
                    
                    # 等待1秒后再处理下一个钱包
                    await asyncio.sleep(1)
                    logging.info(f"等待 1 秒后启动下一个线程...")
                
                self.queue.task_done()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logging.error(f"Worker {worker_id} 发生错误: {str(e)}")
                self.queue.task_done()
                continue
                
    def task_done_callback(self, task):
        """任务完成回调"""
        asyncio.create_task(self.remove_task(task))
        
    async def remove_task(self, task):
        """从运行任务集合中移除任务"""
        async with self.lock:
            self.running_tasks.discard(task)
            
    async def start(self, num_consumers: int):
        """启动指定数量的消费者"""
        self.max_concurrent_tasks = num_consumers
        self.task_semaphore = asyncio.Semaphore(num_consumers)
        self.consumers = [
            asyncio.create_task(self.consumer(i))
            for i in range(num_consumers)
        ]
                
    async def stop(self):
        """停止所有消费者和正在运行的任务"""
        # 取消所有消费者
        for consumer in self.consumers:
            consumer.cancel()
        
        # 等待所有消费者完成
        await asyncio.gather(*self.consumers, return_exceptions=True)
        
        # 等待所有正在运行的任务完成
        async with self.lock:
            running_tasks = list(self.running_tasks)
        if running_tasks:
            await asyncio.gather(*running_tasks, return_exceptions=True)

    async def save_result(self, project_name: str, wallet: WalletInfo, success: bool):
        """保存结果"""
        try:
            # 确保logs项目目录存在
            base_dir = os.path.dirname(os.path.abspath(__file__))  # 获取当前脚本所在目录
            logs_dir = os.path.join(base_dir, "logs")
            project_dir = os.path.join(logs_dir, project_name, self.user_name)
            project_dir = os.path.normpath(project_dir)  # 规范化路径
            
            # 创建目录（如果不存在）
            try:
                os.makedirs(project_dir, exist_ok=True)
            except Exception as e:
                logging.error(f"创建目录失败: {project_dir}, 错误: {str(e)}")
                return
                
            # 保存结果
            result_type = '成功' if success else '失败'
            result_file = os.path.join(project_dir, f'{result_type}_{project_name}.txt')
            result_file = os.path.normpath(result_file)  # 规范化路径
            
            try:
                # 先尝试以同步方式写入
                with open(result_file, 'a', encoding='utf-8') as f:
                    f.write(f"{wallet.original}\n")
                    f.flush()
                    os.fsync(f.fileno())  # 强制写入磁盘
                    
            except Exception as e:
                logging.error(f"写入文件失败: {str(e)}")
                # 如果同步写入失败，尝试异步写入
                try:
                    async with aiofiles.open(result_file, 'a', encoding='utf-8') as f:
                        await f.write(f"{wallet.original}\n")
                        await f.flush()
                except Exception as ae:
                    logging.error(f"异步写入也失败了: {str(ae)}")
                    raise
                
        except Exception as e:
            logging.error(f"保存结果失败: {str(e)}")
            logging.error(f"脚本目录: {base_dir}")
            logging.error(f"项目目录: {project_dir}")
            logging.error(f"结果文件: {result_file if 'result_file' in locals() else '未创建'}")
            # 尝试列出目录内容
            try:
                if os.path.exists(project_dir):
                    logging.error(f"目录内容: {os.listdir(project_dir)}")
                else:
                    logging.error("项目目录不存在")
            except Exception as e:
                logging.error(f"列出目录内容失败: {str(e)}")

    async def check_gas_price(self) -> float:
        """获取当前gas价格"""
        try:
            if not self.web3_pool:
                logging.error("web3_pool未初始化")
                return 0
                
            query_w3 = self.web3_pool.get_query_connection()
            if not query_w3:
                logging.error("查询专用RPC连接未初始化")
                return 0
                
            gas_price = query_w3.eth.gas_price
            base_gas = gas_price / 10**9  # 转换为gwei
            
            # 如果设置了最低gas阈值且当前gas低于该值，则在最低值基础上随机增加0.01-0.9
            if self.min_gas_threshold > 0 and base_gas < self.min_gas_threshold:
                random_addition = random.uniform(0.01, 0.9)
                adjusted_gas = self.min_gas_threshold + random_addition
                logging.info(f"当前gas ({base_gas:.2f} Gwei) 低于最低阈值，调整为: {adjusted_gas:.2f} Gwei")
                return adjusted_gas
            return base_gas
            
        except Exception as e:
            logging.error(f"获取gas价格失败: {str(e)}")
            return 0
            
    async def gas_monitor(self):
        """监控gas价格"""
        low_price_count = 0  # 连续低价次数
        
        while True:
            try:
                current_gas = await self.check_gas_price()
                if current_gas == 0:  # 如果获取gas价格失败
                    # 重要修复：如果当前处于暂停状态，获取gas价格失败不应该改变状态
                    if self.is_paused:
                        logging.warning("获取gas价格失败，保持任务暂停状态")
                    else:
                        logging.warning("获取gas价格失败，继续监控")
                    await asyncio.sleep(10)
                    continue
                    
                if current_gas > self.gas_threshold:
                    if not self.is_paused:
                        self.is_paused = True
                        self.pause_event.clear()
                        logging.warning(f"Gas价格过高 ({current_gas:.2f} Gwei > {self.gas_threshold} Gwei), 暂停所有任务")
                    low_price_count = 0
                else:
                    if self.is_paused:
                        low_price_count += 1
                        logging.info(f"Gas价格正常 ({current_gas:.2f} Gwei), 连续第{low_price_count}次")
                        if low_price_count >= 6:  # 连续6次检测到低价
                            self.is_paused = False
                            self.pause_event.set()
                            logging.info("Gas价格已恢复正常,恢复所有任务")
                            low_price_count = 0
                    else:
                        if low_price_count == 0:  # 只在第一次检测到正常价格时打印
                            logging.info(f"Gas价格正常 ({current_gas:.2f} Gwei)")
                        low_price_count = 0
                        
                await asyncio.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logging.error(f"Gas监控出错: {str(e)}")
                # 重要修复：发生异常时，如果当前处于暂停状态，应该保持暂停状态
                if self.is_paused:
                    logging.warning("Gas监控发生错误，保持任务暂停状态")
                await asyncio.sleep(10)

class ImprovedProjectManager:
    def __init__(self, config_path: str = 'config.json'):
        self.config = self._load_config(config_path)
        self.task_queue = TaskQueue()
        self.web3_pool = Web3Pool(self.config['rpc_urls'])
        self.project_classes = {
            'lido_eigenlayer_stak': LidoEigenLayerStakProject,
            'aave_supply': AaveSupplyProject,
            'weth_deposit': WethDepositProject,
            'arbitrum_bridge': ArbitrumBridgeProject,
            'mint_nft_eternalechoes': MintNftEternalechoesProject,
            'mint_nft_carella': MintNftCarellaProject,
            'mint_nft_aaaa': MintNftAAAAProject,
            'mint_nft_runtomoon': MintNftRuntomoonProject,
            'approve_usdt_aggregation': ApproveUsdtAggregationProject,
            'approve_usdt_uniswap': ApproveUsdtUniswapProject,
            'blur_deposit': BlurDepositProject,
            'mint_nft_rbvrs': MintNftRBVRSProject,
            'mint_nft_poply': MintNftPoplyProject
        }
        self.enabled_projects = None
        self.user_name = None
        self.is_test_mode = False
        
        # 设置TaskQueue的配置和项目类
        self.task_queue.config = self.config
        self.task_queue.project_classes = self.project_classes
        
    def _load_config(self, config_path: str) -> dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logging.error(f"加载配置文件失败: {str(e)}")
            raise
            
    async def get_user_input(self) -> str:
        """获取用户输入"""
        while True:
            user_name = input("\n请输入用户名 (例如: 老叶): ").strip()
            if not user_name:
                print("错误: 用户名不能为空!")
                continue
                
            wallet_file = f"wallet_{user_name}.txt"
            if not os.path.exists(wallet_file):
                print(f"错误: 找不到钱包文件 {wallet_file}")
                print("请确保钱包文件存在后重试!")
                continue
                
            return user_name
            
    async def get_run_mode(self) -> Tuple[bool, int]:
        """获取运行模式和任务模式"""
        while True:
            mode = input("\n请选择运行模式:\n1. 正常模式\n2. 测试模式\n请输入 (1/2): ").strip()
            if mode == "1":
                # 获取任务模式
                while True:
                    task_mode = input("\n请选择任务模式:\n1. 完成所有未完成任务\n2. 随机完成1个未完成任务\n3. 随机完成2个未完成任务\n4. 随机完成3个未完成任务\n请输入 (1/2/3/4): ").strip()
                    if task_mode in ["1", "2", "3", "4"]:
                        return False, int(task_mode)
                    print("错误: 请输入1、2、3或4!")
            elif mode == "2":
                return True, 1  # 测试模式默认完成所有任务
            print("错误: 请输入1或2!")
            
    async def get_test_wallet_index(self, total_wallets: int) -> int:
        """获取测试钱包索引"""
        while True:
            try:
                index = input(f"\n请输入要测试的钱包编号 (1-{total_wallets}，默认1): ").strip()
                if not index:
                    return 0
                index = int(index) - 1
                if 0 <= index < total_wallets:
                    return index
                print(f"错误: 请输入1-{total_wallets}之间的数字!")
            except ValueError:
                print("错误: 请输入有效的数字!")
                
    async def get_test_project(self) -> str:
        """获取要测试的项目"""
        enabled_projects = self.get_enabled_projects()
        while True:
            print("\n可用的测试项目:")
            for i, project in enumerate(enabled_projects, 1):
                print(f"{i}. {project}")
                
            try:
                choice = input("\n请选择要测试的项目编号: ").strip()
                index = int(choice) - 1
                if 0 <= index < len(enabled_projects):
                    return enabled_projects[index]
                print(f"错误: 请输入1-{len(enabled_projects)}之间的数字!")
            except ValueError:
                print("错误: 请输入有效的数字!")
                
    def setup_logging(self, user_name: str):
        """设置日志"""
        # 确保logs目录存在
        if not os.path.exists('logs'):
            os.makedirs('logs')
            
        # 创建用户特定的日志目录
        user_log_dir = os.path.join('logs', user_name)
        if not os.path.exists(user_log_dir):
            os.makedirs(user_log_dir)
            
        # 设置日志文件
        log_file = os.path.join(user_log_dir, 'run.log')
        
        # 移除现有的处理程序
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
            
        # 配置新的日志处理程序
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        
    def get_enabled_projects(self) -> List[str]:
        """获取启用的项目列表"""
        if self.enabled_projects is None:
            self.enabled_projects = [
                name for name, enabled in self.config['enabled_projects'].items() 
                if enabled
            ]
            
            # 预先检查并创建项目目录
            print("\n检查项目目录...")
            base_dir = os.path.dirname(os.path.abspath(__file__))  # 获取当前脚本所在目录
            logs_dir = os.path.join(base_dir, "logs")
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir)
                
            for i, project_name in enumerate(self.enabled_projects, 1):
                progress = i / len(self.enabled_projects)
                bar_length = 50
                filled_length = int(bar_length * progress)
                bar = '=' * filled_length + '-' * (bar_length - filled_length)
                print(f'\r项目检查进度: [{bar}] {progress:.1%} ({i}/{len(self.enabled_projects)})', end='', flush=True)
                
                # 确保项目目录存在于logs目录下
                project_dir = os.path.join(logs_dir, project_name, self.user_name)
                if not os.path.exists(project_dir):
                    os.makedirs(project_dir)
                    # 创建空的记录文件
                    open(os.path.join(project_dir, f'成功_{project_name}.txt'), 'a').close()
                    open(os.path.join(project_dir, f'失败_{project_name}.txt'), 'a').close()
                    
            print()  # 换行
        
        return self.enabled_projects
        
    async def load_wallets(self, user_name: str) -> List[WalletInfo]:
        """加载钱包文件"""
        wallets = []
        wallet_file = f"wallet_{user_name}.txt"
        
        print("\n开始读取钱包文件...")
        try:
            # 先统计总行数
            total_lines = sum(1 for line in open(wallet_file, 'r') if line.strip())
            
            # 读取并处理钱包
            with open(wallet_file, 'r') as f:
                for i, line in enumerate(f, 1):
                    if line.strip():
                        try:
                            # 显示进度条
                            progress = i / total_lines
                            bar_length = 50
                            filled_length = int(bar_length * progress)
                            bar = '=' * filled_length + '-' * (bar_length - filled_length)
                            print(f'\r读取进度: [{bar}] {progress:.1%} ({i}/{total_lines})', end='', flush=True)
                            
                            wallet = WalletInfo(line.strip())
                            wallets.append(wallet)
                        except ValueError as e:
                            logging.error(f"无效的钱包: {str(e)}")
                            continue
            print()  # 换行
            
            if not wallets:
                print("错误: 没有有效的钱包!")
                return []
                
            print(f"成功读取 {len(wallets)} 个有效钱包")
            return wallets
            
        except Exception as e:
            logging.error(f"读取钱包文件失败: {str(e)}")
            return []
            
    async def load_project_records(self, project_name: str) -> Set[str]:
        """加载项目记录"""
        records = set()
        base_dir = os.path.dirname(os.path.abspath(__file__))  # 获取当前脚本所在目录
        logs_dir = os.path.join(base_dir, "logs")
        project_dir = os.path.join(logs_dir, project_name, self.user_name)
        
        for record_type in ['成功', '失败']:
            record_file = os.path.join(project_dir, f'{record_type}_{project_name}.txt')
            if os.path.exists(record_file):
                try:
                    async with aiofiles.open(record_file, 'r') as f:
                        content = await f.read()
                        for line in content.splitlines():
                            if line.strip():
                                try:
                                    wallet = WalletInfo(line.strip())
                                    records.add(wallet.address)
                                except ValueError:
                                    continue
                except Exception as e:
                    logging.error(f"读取记录文件 {record_file} 失败: {str(e)}")
        
        return records
 
    async def analyze_wallets(self, wallets: List[WalletInfo], task_mode: int = 1) -> Tuple[List[WalletInfo], Dict[str, List[str]]]:
        """分析钱包完成情况"""
        if self.is_test_mode:
            # 测试模式下不需要分析完成情况
            return [], wallets, {wallet.address: self.get_enabled_projects() for wallet in wallets}
            
        # 并发加载所有项目记录
        project_records = {}
        tasks = []
        enabled_projects = self.get_enabled_projects()
        
        for project_name in enabled_projects:
            task = asyncio.create_task(self.load_project_records(project_name))
            tasks.append((project_name, task))
        
        # 等待所有记录加载完成
        for project_name, task in tasks:
            project_records[project_name] = await task
            
        # 分析钱包
        completed_wallets = []
        pending_wallets = []
        wallet_pending_projects = {}  # 记录每个钱包待完成的项目
        total = len(wallets)
        
        print("\n开始分析钱包完成情况...")
        for i, wallet in enumerate(wallets, 1):
            # 显示进度条
            progress = i / total
            bar_length = 50
            filled_length = int(bar_length * progress)
            bar = '=' * filled_length + '-' * (bar_length - filled_length)
            print(f'\r分析进度: [{bar}] {progress:.1%} ({i}/{total})', end='', flush=True)
            
            # 检查每个项目的完成情况
            pending_projects = []
            for project_name in enabled_projects:
                if wallet.address not in project_records[project_name]:
                    pending_projects.append(project_name)
                    
            if pending_projects:
                pending_wallets.append(wallet)
                if task_mode == 2:  # 随机选择一个未完成任务
                    random_project = random.choice(pending_projects)
                    wallet_pending_projects[wallet.address] = [random_project]
                elif task_mode == 3:  # 随机选择两个未完成任务
                    if len(pending_projects) >= 2:  # 确保有足够的未完成任务
                        random_projects = random.sample(pending_projects, 2)
                        wallet_pending_projects[wallet.address] = random_projects
                    else:  # 如果未完成任务少于2个，则全部执行
                        wallet_pending_projects[wallet.address] = pending_projects
                elif task_mode == 4:  # 随机选择三个未完成任务
                    if len(pending_projects) >= 3:  # 确保有足够的未完成任务
                        random_projects = random.sample(pending_projects, 3)
                        wallet_pending_projects[wallet.address] = random_projects
                    else:  # 如果未完成任务少于3个，则全部执行
                        wallet_pending_projects[wallet.address] = pending_projects
                else:  # 完成所有未完成任务
                    wallet_pending_projects[wallet.address] = pending_projects
            else:
                completed_wallets.append(wallet)
                
        print()  # 换行
        
        if not self.is_test_mode:
            # 打印分析结果
            print(f"\n钱包分析完成:")
            print(f"总钱包数: {total}")
            print(f"已完成全部任务: {len(completed_wallets)}")
            print(f"需要执行任务: {len(pending_wallets)}")
            
            if task_mode == 2:
                print("\n随机任务模式: 每个钱包随机完成1个未完成任务")
            elif task_mode == 3:
                print("\n随机任务模式: 每个钱包随机完成2个未完成任务")
            elif task_mode == 4:
                print("\n随机任务模式: 每个钱包随机完成3个未完成任务")
            else:
                print("\n全部任务模式:")
                
            # 打印每个待处理钱包需要完成的项目数
            if pending_wallets:
                print("\n待处理钱包项目统计:")
                for wallet in pending_wallets[:5]:  # 只显示前5个钱包的统计
                    pending_count = len(wallet_pending_projects[wallet.address])
                    projects = wallet_pending_projects[wallet.address]
                    print(f"钱包 {wallet.address[:6]}...{wallet.address[-4:]}: 待完成 {pending_count} 个项目 ({', '.join(projects)})")
                if len(pending_wallets) > 5:
                    print("... 等")
        
        return completed_wallets, pending_wallets, wallet_pending_projects
            
    async def run(self):
        """运行改进后的主流程"""
        while True:
            try:
                # 1. 初始化Web3连接池
                await self.web3_pool.initialize()
                self.task_queue.web3_pool = self.web3_pool
                self.web3_pool.set_task_queue(self.task_queue)  # 设置TaskQueue引用
                
                # 2. 获取用户输入
                self.user_name = await self.get_user_input()
                if not self.user_name:
                    return
                    
                # 3. 获取运行模式和任务模式
                self.is_test_mode, task_mode = await self.get_run_mode()
                
                # 4. 设置日志
                self.setup_logging(self.user_name)
                
                # 5. 设置TaskQueue的用户名
                self.task_queue.user_name = self.user_name
                
                # 6. 加载钱包
                wallets = await self.load_wallets(self.user_name)
                if not wallets:
                    continue
                    
                if self.is_test_mode:
                    # 测试模式特殊处理
                    # 6.1 选择测试钱包
                    test_wallet_index = await self.get_test_wallet_index(len(wallets))
                    test_wallet = wallets[test_wallet_index]
                    wallets = [test_wallet]  # 只使用测试钱包
                    
                    # 6.2 选择测试项目
                    test_project = await self.get_test_project()
                    
                    # 6.3 初始化测试钱包的nonce
                    w3 = await self.web3_pool.get_connection()
                    test_wallet.nonce = w3.eth.get_transaction_count(test_wallet.address)
                    test_wallet.initial_nonce = test_wallet.nonce
                    
                    # 6.4 创建特定项目的任务
                    wallet_pending_projects = {test_wallet.address: [test_project]}
                    completed_wallets = []
                    pending_wallets = wallets
                    
                else:
                    # 7. 获取并发数
                    while True:
                        try:
                            concurrent = input("\n请输入并发数 (默认1): ").strip()
                            concurrent = int(concurrent) if concurrent else 1
                            if concurrent < 1:
                                print("错误: 并发数必须大于0!")
                                continue
                            break
                        except ValueError:
                            print("错误: 请输入有效的数字!")
                    
                    # 8. 获取gas阈值
                    while True:
                        try:
                            gas_threshold = input("\n请输入gas阈值 (默认1 Gwei): ").strip()
                            gas_threshold = float(gas_threshold) if gas_threshold else 1
                            if gas_threshold <= 0:
                                print("错误: gas阈值必须大于0!")
                                continue
                            break
                        except ValueError:
                            print("错误: 请输入有效的数字!")
                    
                    # 8.1 获取最低gas阈值
                    while True:
                        try:
                            min_gas = input("\n请输入最低gas阈值 (默认0，表示不设置): ").strip()
                            min_gas = float(min_gas) if min_gas else 0
                            if min_gas < 0:
                                print("错误: 最低gas阈值不能小于0!")
                                continue
                            break
                        except ValueError:
                            print("错误: 请输入有效的数字!")
                    
                    # 8.2 获取钱包间隔休息时间最小值
                    while True:
                        try:
                            wallet_sleep_min = input("\n请输入钱包间隔休息时间最小值 (默认1秒): ").strip()
                            wallet_sleep_min = float(wallet_sleep_min) if wallet_sleep_min else 1
                            if wallet_sleep_min < 0:
                                print("错误: 休息时间不能小于0!")
                                continue
                            break
                        except ValueError:
                            print("错误: 请输入有效的数字!")
                    
                    # 8.3 获取钱包间隔休息时间最大值
                    while True:
                        try:
                            wallet_sleep_max = input("\n请输入钱包间隔休息时间最大值 (默认3秒): ").strip()
                            wallet_sleep_max = float(wallet_sleep_max) if wallet_sleep_max else 3
                            if wallet_sleep_max < wallet_sleep_min:
                                print(f"错误: 最大值必须大于或等于最小值 {wallet_sleep_min}!")
                                continue
                            break
                        except ValueError:
                            print("错误: 请输入有效的数字!")
                    
                    # 设置钱包间隔休息时间
                    self.task_queue.wallet_sleep_min = wallet_sleep_min
                    self.task_queue.wallet_sleep_max = wallet_sleep_max
                    logging.info(f"设置钱包间隔休息时间: {wallet_sleep_min}-{wallet_sleep_max}秒")
                    
                    # 9. 分析钱包完成情况
                    completed_wallets, pending_wallets, wallet_pending_projects = await self.analyze_wallets(wallets, task_mode)
                    if not pending_wallets and not self.is_test_mode:
                        print("\n所有钱包已完成全部任务!")
                        break
                
                # 10. 初始化查询专用连接
                if self.config['query_rpc']:
                    self.web3_pool.initialize_query_connection(self.config['query_rpc'])
                    logging.info("初始化查询专用RPC连接成功")
                else:
                    logging.error("配置文件中缺少query_rpc设置")
                    continue
                
                if not self.is_test_mode:
                    # 11. 设置gas阈值并启动监控
                    self.task_queue.gas_threshold = gas_threshold
                    self.task_queue.min_gas_threshold = min_gas  # 设置最低gas阈值
                    self.task_queue.gas_monitor_task = asyncio.create_task(self.task_queue.gas_monitor())
                    logging.info(f"启动gas监控 (阈值: {gas_threshold} Gwei, 最低阈值: {min_gas} Gwei)")
                    
                    # 12. 启动任务队列
                    await self.task_queue.start(concurrent)
                else:
                    # 测试模式只启动一个worker
                    await self.task_queue.start(1)
                
                # 13. 启动生产者并等待队列完成
                await self.task_queue.producer(pending_wallets, wallet_pending_projects)
                await self.task_queue.queue.join()
                
                # 14. 等待所有正在运行的任务完成
                async with self.task_queue.lock:
                    running_tasks = list(self.task_queue.running_tasks)
                if running_tasks:
                    await asyncio.gather(*running_tasks, return_exceptions=True)
                
                # 15. 停止任务队列
                await self.task_queue.stop()
                
                # 16. 停止gas监控
                if not self.is_test_mode and self.task_queue.gas_monitor_task:
                    self.task_queue.gas_monitor_task.cancel()
                    try:
                        await self.task_queue.gas_monitor_task
                    except asyncio.CancelledError:
                        pass
                
                logging.info("所有任务完成!")
                break
                
            except Exception as e:
                logging.error(f"程序运行出错: {str(e)}")
                retry = input("\n是否重试? (y/n): ").strip().lower()
                if retry != 'y':
                    break

class MintNftRBVRSProject(ProjectBase):
    def __init__(self, wallet: WalletInfo, config: dict):
        super().__init__(wallet, config)
        self.project_config = config['project_settings']['mint_nft_rbvrs']
        
        # 将合约地址转换为checksum格式
        self.rbvrs_contract = Web3.to_checksum_address(self.project_config['rbvrs_contract'])
        self.nft_contract = Web3.to_checksum_address(self.project_config['nft_contract'])
        self.fee_recipient = Web3.to_checksum_address(self.project_config['fee_recipient'])
        
        # Mint NFT ABI
        self.rbvrs_abi = [{
            "inputs": [
                {"name": "nftContract", "type": "address"},
                {"name": "feeRecipient", "type": "address"},
                {"name": "minterIfNotPayer", "type": "address"},
                {"name": "quantity", "type": "uint256"}
            ],
            "name": "mintPublic",
            "outputs": [],
            "stateMutability": "nonpayable",
            "type": "function"
        }]
        
    async def mint_nft(self) -> bool:
        """铸造RBVRS NFT"""
        try:
            contract = self.w3.eth.contract(
                address=self.rbvrs_contract,
                abi=self.rbvrs_abi
            )
            
            # 使用钱包自己的地址作为minterIfNotPayer
            minter = self.wallet.address
            # 随机铸造数量，3-5个
            quantity = random.randint(3, 5)
            
            gas_price = await self.get_gas_price()
            gas_limit = random.randint(180000, 200000)  # 随机gas limit
            
            tx = contract.functions.mintPublic(
                self.nft_contract,
                self.fee_recipient,
                minter,
                quantity
            ).build_transaction({
                'from': self.wallet.address,
                'nonce': self.wallet.nonce,
                'gas': gas_limit,
                'maxFeePerGas': gas_price,
                'maxPriorityFeePerGas': gas_price,
                'chainId': 1  # 以太坊主网
            })
            
            signed_tx = self.w3.eth.account.sign_transaction(tx, self.wallet.original)
            success = await self.send_transaction_with_retry(signed_tx, f"铸造RBVRS NFT (数量: {quantity}个)")
            return success
            
        except Exception as e:
            logging.error(f"铸造RBVRS NFT失败: {str(e)}")
            return False
            
    async def execute(self) -> bool:
        """执行RBVRS NFT铸造流程"""
        try:
            # 执行铸造操作
            success = await self.mint_nft()
            return success
            
        except Exception as e:
            logging.error(f"RBVRS NFT铸造流程失败: {str(e)}")
            return False

class MintNft10YETHProject(ProjectBase):
    def __init__(self, wallet: WalletInfo, config: dict):
        super().__init__(wallet, config)
        self.project_config = config['project_settings']['mint_nft_10yeth']
        # 将合约地址转换为checksum格式
        self.nft_contract = Web3.to_checksum_address(self.project_config['nft_contract'])
        self.transaction_data = self.project_config['transaction_data']

    async def mint_nft(self) -> bool:
        """铸造10YETH NFT - 直接发送指定的data"""
        try:
            # 使用固定的Gas价格而非动态获取
            max_priority_fee_per_gas = int(0.15 * 10**9)  # 0.15 Gwei
            max_fee_per_gas = int(1.8 * 10**9)  # 1.8 Gwei

            # 随机选择Gas Limit
            gas_limit = random.randint(155600, 157656)

            # 构建交易
            tx = {
                'from': self.wallet.address,
                'to': self.nft_contract,
                'value': 0,  # 无需支付ETH
                'data': self.transaction_data,
                'nonce': self.wallet.nonce,
                'gas': gas_limit,
                'maxFeePerGas': max_fee_per_gas,
                'maxPriorityFeePerGas': max_priority_fee_per_gas,
                'chainId': 1  # 以太坊主网
            }

            signed_tx = self.w3.eth.account.sign_transaction(tx, self.wallet.original)
            success = await self.send_transaction_with_retry(signed_tx, "铸造10YETH NFT")
            return success

        except Exception as e:
            logging.error(f"铸造10YETH NFT失败: {str(e)}")
            return False

    async def execute(self) -> bool:
        """执行10YETH NFT铸造流程"""
        try:
            # 执行铸造操作
            success = await self.mint_nft()
            return success

        except Exception as e:
            logging.error(f"10YETH NFT铸造流程失败: {str(e)}")
            return False

class MintNftPoplyProject(ProjectBase):
    def __init__(self, wallet: WalletInfo, config: dict):
        super().__init__(wallet, config)
        self.project_config = config['project_settings']['mint_nft_poply']
        # 将合约地址转换为checksum格式
        self.nft_contract = Web3.to_checksum_address(self.project_config['nft_contract'])
        
        # Mint NFT ABI
        self.nft_abi = [{
            "inputs": [
                {"name": "_count", "type": "uint256"}
            ],
            "name": "mint",
            "outputs": [],
            "stateMutability": "payable",
            "type": "function"
        }, {
            "constant": True,
            "inputs": [],
            "name": "totalSupply",
            "outputs": [{"name": "", "type": "uint256"}],
            "payable": False,
            "stateMutability": "view",
            "type": "function"
        }]
        
    async def check_balance(self) -> bool:
        """检查钱包余额是否足够"""
        try:
            # 获取最大铸造数量
            max_mint_count = 3
            # 计算最大需要的金额（假设每个单价为0.01）
            max_required_amount = max_mint_count * 0.01
            
            balance = self.w3.eth.get_balance(self.wallet.address) / 10**18
            
            if balance < max_required_amount:
                logging.warning(f"{self.get_wallet_prefix()} 钱包余额 ({balance:.6f} MON) 不足以铸造 Poply NFT (需要 {max_required_amount:.6f} MON)")
                return False
                
            logging.info(f"{self.get_wallet_prefix()} 钱包余额 ({balance:.6f} MON) 足够铸造 Poply NFT")
            return True
            
        except Exception as e:
            logging.error(f"{self.get_wallet_prefix()} 检查钱包余额失败: {str(e)}")
            return False
            
    async def check_total_supply(self) -> bool:
        """检查当前NFT总供应量是否超过限制"""
        try:
            contract = self.w3.eth.contract(
                address=self.nft_contract,
                abi=self.nft_abi
            )
            
            total_supply = contract.functions.totalSupply().call()
            max_supply = 650000
            
            if total_supply >= max_supply:
                logging.warning(f"{self.get_wallet_prefix()} Poply NFT 当前供应量 ({total_supply}) 已达到或超过上限 ({max_supply})，跳过铸造")
                return False
                
            logging.info(f"{self.get_wallet_prefix()} Poply NFT 当前供应量: {total_supply}/{max_supply}")
            return True
            
        except Exception as e:
            logging.error(f"{self.get_wallet_prefix()} 检查NFT总供应量失败: {str(e)}")
            return False
    
    async def get_gas_price_with_custom_increase(self) -> int:
        """获取gas price并增加适当的值"""
        try:
            # 使用查询专用RPC获取实时费率
            if self.query_w3 is None:
                self.query_w3 = await self.get_web3_for_query()
                
            if not self.query_w3:
                logging.error(f"{self.get_wallet_prefix()} 查询专用RPC连接未初始化")
                return int(150 * 10**9)  # 使用默认gas价格
                
            base_gas_price = self.query_w3.eth.gas_price
            gas_price_gwei = base_gas_price / 10**9
            
            # 在当前gas基础上增加5-10%
            increase_percentage = random.uniform(1.05, 1.1)
            gas_price_with_buffer = int(base_gas_price * increase_percentage)
            
            # 如果设置了最低gas阈值且当前gas低于该值，则在最低值基础上随机增加0.01-0.9
            if hasattr(self.w3, 'web3_pool') and hasattr(self.w3.web3_pool, 'task_queue'):
                min_gas_threshold = getattr(self.w3.web3_pool.task_queue, 'min_gas_threshold', 0)
                if min_gas_threshold > 0 and gas_price_gwei < min_gas_threshold:
                    random_addition = random.uniform(0.01, 0.9)
                    adjusted_gas = min_gas_threshold + random_addition
                    logging.info(f"{self.get_wallet_prefix()} 当前gas ({gas_price_gwei:.2f} Gwei) 低于最低阈值，调整为: {adjusted_gas:.2f} Gwei")
                    return int(adjusted_gas * 10**9)
            
            self.wallet.current_gas_gwei = gas_price_gwei
            logging.info(f"{self.get_wallet_prefix()} 使用当前费率: {gas_price_gwei:.2f} Gwei (+{(increase_percentage-1)*100:.1f}% = {(gas_price_gwei * increase_percentage):.2f} Gwei)")
            return gas_price_with_buffer
            
        except Exception as e:
            logging.error(f"{self.get_wallet_prefix()} 获取gas价格失败: {str(e)}")
            # 使用默认gas价格
            default_gwei = 150
            return int(default_gwei * 10**9)
    
    async def estimate_gas(self, mint_count: int) -> int:
        """估算Gas Limit"""
        # 基础 Gas Limit：309255
        # 每增加一个 增加51000
        base_gas = 309255
        extra_gas_per_item = 51000
        
        # 计算总Gas Limit
        if mint_count == 1:
            gas_limit = base_gas
        else:
            gas_limit = base_gas + (mint_count - 1) * extra_gas_per_item
            
        # 添加0-3%的随机buffer
        buffer = random.uniform(1.0, 1.03)
        final_gas_limit = int(gas_limit * buffer)
        
        logging.info(f"{self.get_wallet_prefix()} 估算Gas Limit: {final_gas_limit} (基础: {base_gas}, 铸造数量: {mint_count}, 总buffer: {buffer:.2f})")
        return final_gas_limit
            
    async def mint_nft(self) -> bool:
        """铸造Poply NFT"""
        try:
            # 先检查总供应量
            if not await self.check_total_supply():
                return False
                
            contract = self.w3.eth.contract(
                address=self.nft_contract,
                abi=self.nft_abi
            )
            
            # 随机选择铸造数量(1-3)
            mint_count = random.randint(1, 3)
            
            # 计算支付金额
            price_per_item = 0.01  # 每个单价为0.01 MON
            total_value = mint_count * price_per_item
            value_wei = int(total_value * 10**18)
            
            gas_price = await self.get_gas_price_with_custom_increase()
            gas_limit = await self.estimate_gas(mint_count)
            
            tx = contract.functions.mint(
                mint_count  # 铸造数量
            ).build_transaction({
                'from': self.wallet.address,
                'value': value_wei,
                'nonce': self.wallet.nonce,
                'gas': gas_limit,
                'maxFeePerGas': gas_price,
                'maxPriorityFeePerGas': gas_price,
                'chainId': 1  # 以太坊主网
            })
            
            signed_tx = self.w3.eth.account.sign_transaction(tx, self.wallet.original)
            success = await self.send_transaction_with_retry(signed_tx, f"铸造{mint_count}个Poply NFT (支付: {total_value} MON)")
            return success
            
        except Exception as e:
            logging.error(f"{self.get_wallet_prefix()} 铸造Poply NFT失败: {str(e)}")
            return False
            
    async def execute(self) -> bool:
        """执行Poply NFT铸造流程"""
        try:
            # 检查钱包余额
            if not await self.check_balance():
                return False
                
            # 执行铸造操作
            success = await self.mint_nft()
            return success
            
        except Exception as e:
            logging.error(f"{self.get_wallet_prefix()} Poply NFT铸造流程失败: {str(e)}")
            return False

async def main():
    try:
        manager = ImprovedProjectManager()
        await manager.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
