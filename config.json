{"rpc_urls": ["https://ethereum.blockpi.network/v1/rpc/c6eef8489abd1889f6cec77dbbc7d75b6680d8ca"], "query_rpc": "https://ethereum.blockpi.network/v1/rpc/c6eef8489abd1889f6cec77dbbc7d75b6680d8ca", "enabled_projects": {"lido_eigenlayer_stak": true, "aave_supply": true, "arbitrum_bridge": true, "weth_deposit": true, "mint_nft_eternalechoes": false, "mint_nft_carella": true, "mint_nft_aaaa": false, "mint_nft_runtomoon": false, "approve_usdt_aggregation": true, "approve_usdt_uniswap": true, "blur_deposit": true, "mint_nft_rbvrs": false, "mint_nft_poply": false}, "project_settings": {"lido_eigenlayer_stak": {"lido_contract": "******************************************", "eigenlayer_contract": "******************************************", "referral": "******************************************", "strategy": "******************************************", "min_amount": "0.0001", "max_amount": "0.00015", "use_query_rpc": false}, "aave_supply": {"contract_address": "******************************************", "pool_address": "******************************************", "min_amount": "0.00005", "max_amount": "0.0001", "use_query_rpc": false}, "arbitrum_bridge": {"bridge_contract": "******************************************", "min_amount": "0.00005", "max_amount": "0.0001", "use_query_rpc": false}, "weth_deposit": {"weth_contract": "******************************************", "min_amount": "0.00005", "max_amount": "0.0001", "use_query_rpc": false}, "mint_nft_eternalechoes": {"nft_contract": "******************************************", "use_query_rpc": false}, "mint_nft_carella": {"nft_contract": "******************************************", "use_query_rpc": false}, "mint_nft_aaaa": {"nft_contract": "******************************************", "use_query_rpc": false}, "mint_nft_runtomoon": {"nft_contract": "******************************************", "use_query_rpc": false}, "approve_usdt_aggregation": {"usdt_contract": "******************************************", "spender": "******************************************", "use_query_rpc": false}, "approve_usdt_uniswap": {"usdt_contract": "******************************************", "spender": "******************************************", "use_query_rpc": false}, "blur_deposit": {"blur_contract": "******************************************", "min_amount": "0.00005", "max_amount": "0.0001", "use_query_rpc": false}, "mint_nft_rbvrs": {"rbvrs_contract": "0x00005EA00Ac477B1030CE78506496e8C2dE24bf5", "nft_contract": "0x5782Bfff4a063Db1E1Fbb15941CbCB0bAe6301a6", "fee_recipient": "0x0000a26b00c1F0DF003000390027140000fAa719", "use_query_rpc": false}, "mint_nft_poply": {"nft_contract": "0xD97BCe4518b886A36e345764333d77b5fAF6FE2C", "use_query_rpc": false}}}